"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect, use } from "react";

interface ScreenshotPageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function ScreenshotPage({ params }: ScreenshotPageProps) {
  const [screenshotExists, setScreenshotExists] = useState<boolean | null>(null);
  const [imageError, setImageError] = useState(false);
  const userId = use(params).userId;

  // Check if screenshot exists
  useEffect(() => {
    const checkScreenshot = async () => {
      try {
        const response = await fetch(`/uploads/screenshot-${userId}.png`, {
          method: 'HEAD'
        });
        setScreenshotExists(response.ok);
      } catch (error) {
        setScreenshotExists(false);
      }
    };

    checkScreenshot();
  }, [userId]);

  const handleImageError = () => {
    setImageError(true);
    setScreenshotExists(false);
  };

  if (screenshotExists === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking for screenshot...</p>
        </div>
      </div>
    );
  }

  if (!screenshotExists || imageError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">📷</div>
          <h1 className="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-200">
            No Screenshot Found
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            No screenshot exists for session ID:
          </p>
          <p className="text-lg font-mono font-bold text-blue-600 dark:text-blue-400 mb-6">
            {userId}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
            The user may not have taken a screenshot yet, or the session ID might be incorrect.
          </p>
          <Link
            href="/"
            className="inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Go to Screen Share
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4 text-gray-800 dark:text-gray-200">
            Screenshot for Session
          </h1>
          <div className="inline-block p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <p className="text-sm text-gray-600 dark:text-gray-400">Session ID:</p>
            <p className="text-xl font-mono font-bold text-blue-600 dark:text-blue-400">
              {userId}
            </p>
          </div>
        </div>

        {/* Screenshot Display */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="relative">
            <Image
              src={`/uploads/screenshot-${userId}.png`}
              alt={`Screenshot for session ${userId}`}
              width={1920}
              height={1080}
              className="w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700"
              onError={handleImageError}
              priority
            />
          </div>

          {/* Actions */}
          <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href={`/uploads/screenshot-${userId}.png`}
              download={`screenshot-${userId}.png`}
              className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-center"
            >
              Download Screenshot
            </a>
            <Link
              href="/"
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-center"
            >
              Take New Screenshot
            </Link>
          </div>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>This screenshot is automatically updated when a new one is uploaded for this session.</p>
        </div>
      </div>
    </div>
  );
}
