"use client";

import Link from "next/link";
import { useState, useEffect, use } from "react";

interface ScreenshotPageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function ScreenshotPage({ params }: ScreenshotPageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLive, setIsLive] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [posterUrl, setPosterUrl] = useState<string>('');
  const userId = use(params).userId;

  // Poll for image updates and update video poster
  useEffect(() => {
    if (!isLive) return;

    const updatePoster = () => {
      const timestamp = Date.now();
      setPosterUrl(`/uploads/screenshot-${userId}.png?t=${timestamp}`);
      setLastUpdate(new Date());
    };

    // Initial load
    updatePoster();

    const pollInterval = setInterval(updatePoster, 1000); // Poll every 1 second

    return () => clearInterval(pollInterval);
  }, [isLive, userId]);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageError(false);
  };

  const toggleLive = () => {
    setIsLive(!isLive);
  };

  if (imageError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">📷</div>
          <h1 className="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-200">
            No Screenshot Found
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            No screenshot exists for session ID:
          </p>
          <p className="text-lg font-mono font-bold text-blue-600 dark:text-blue-400 mb-6">
            {userId}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
            The user may not have taken a screenshot yet, or the session ID might be incorrect.
          </p>
          <Link
            href="/"
            className="inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Go to Screen Share
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4 text-gray-800 dark:text-gray-200">
            Live Screen Share
          </h1>
          <div className="inline-block p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <p className="text-sm text-gray-600 dark:text-gray-400">Session ID:</p>
            <p className="text-xl font-mono font-bold text-blue-600 dark:text-blue-400">
              {userId}
            </p>
            <div className="flex items-center justify-center gap-2 mt-2">
              <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {isLive ? 'LIVE' : 'PAUSED'}
              </span>
              {lastUpdate && (
                <span className="text-xs text-gray-500 ml-2">
                  Updated: {lastUpdate.toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Live Video Display */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="relative">
            <video
              poster={posterUrl}
              className="w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700"
              controls
              playsInline
              onError={handleImageError}
              onLoadedData={handleImageLoad}
            >
              <source src="" type="video/mp4" />
              Your browser does not support the video tag.
            </video>

            {/* Live indicator overlay */}
            <div className="absolute top-4 left-4 flex items-center gap-2 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm">
              <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
              {isLive ? 'LIVE' : 'PAUSED'}
            </div>
          </div>

          {/* Controls */}
          <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={toggleLive}
              className={`px-6 py-3 rounded-lg transition-colors text-center font-medium ${isLive
                ? 'bg-red-500 text-white hover:bg-red-600'
                : 'bg-green-500 text-white hover:bg-green-600'
                }`}
            >
              {isLive ? 'Pause Live Feed' : 'Resume Live Feed'}
            </button>
            <Link
              href="/"
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-center"
            >
              Start Your Own Share
            </Link>
          </div>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>Live feed updates every second when new screenshots are uploaded for this session.</p>
          <p className="mt-1">Use the pause button to stop automatic updates.</p>
        </div>
      </div>
    </div>
  );
}
