"use client";

import Link from "next/link";
import { useState, useEffect, useRef, use } from "react";

interface ScreenshotPageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function ScreenshotPage({ params }: ScreenshotPageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLive, setIsLive] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [streamReady, setStreamReady] = useState(false);
  const [lastModified, setLastModified] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const latestRequestRef = useRef<number>(0); // Track latest request timestamp
  const userId = use(params).userId;

  // Create video stream from canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    const video = videoRef.current;

    if (!canvas || !video) return;

    // Set up canvas
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Create MediaStream from canvas
    const stream = canvas.captureStream(30); // 30 FPS
    streamRef.current = stream;
    video.srcObject = stream;
    setStreamReady(true);

    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  // Poll for image updates and draw to canvas (only when changed)
  useEffect(() => {
    if (!isLive || !streamReady) return;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    const checkForUpdates = async () => {
      try {
        // First, check if the file has been modified using HEAD request
        const headResponse = await fetch(`/uploads/screenshot-${userId}.png`, {
          method: 'HEAD',
          cache: 'no-cache'
        });

        if (!headResponse.ok) {
          setImageError(true);
          return;
        }

        const currentLastModified = headResponse.headers.get('Last-Modified');

        // Only download if the file has been modified since last check
        if (currentLastModified && currentLastModified !== lastModified) {
          console.log('New screenshot detected, downloading...');

          // Generate unique request ID to track this specific request
          const requestTimestamp = Date.now();
          latestRequestRef.current = requestTimestamp;

          const img = new Image();
          img.crossOrigin = 'anonymous';

          img.onload = () => {
            // Only update canvas if this is still the latest request
            if (requestTimestamp === latestRequestRef.current) {
              console.log('Applying latest image to canvas');

              // Set canvas size to match image
              canvas.width = img.width;
              canvas.height = img.height;

              // Clear canvas and draw new image
              ctx.clearRect(0, 0, canvas.width, canvas.height);
              ctx.drawImage(img, 0, 0);

              setLastUpdate(new Date());
              setLastModified(currentLastModified);
              setImageError(false);
            } else {
              console.log('Ignoring outdated image response');
            }
          };

          img.onerror = () => {
            // Only set error if this is still the latest request
            if (requestTimestamp === latestRequestRef.current) {
              setImageError(true);
            }
          };

          // Use cache busting when we know there's a new image to force browser refresh
          img.src = `/uploads/screenshot-${userId}.png?t=${requestTimestamp}`;
        } else if (currentLastModified) {
          // File exists but hasn't changed - no download needed
          console.log('No changes detected, skipping download');
        }
      } catch (error) {
        console.error('Error checking for updates:', error);
        setImageError(true);
      }
    };

    // Initial load
    checkForUpdates();

    const pollInterval = setInterval(checkForUpdates, 1000); // Check every 1 second

    return () => clearInterval(pollInterval);
  }, [isLive, userId, streamReady, lastModified]);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageError(false);
  };

  const toggleLive = () => {
    setIsLive(!isLive);
  };

  if (imageError) {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          <div className="text-6xl mb-4">📷</div>
          <h1 className="text-2xl font-bold mb-4 text-gray-800 dark:text-gray-200">
            No Screenshot Found
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-2">
            No screenshot exists for session ID:
          </p>
          <p className="text-lg font-mono font-bold text-blue-600 dark:text-blue-400 mb-6">
            {userId}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
            The user may not have taken a screenshot yet, or the session ID might be incorrect.
          </p>
          <Link
            href="/"
            className="inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Go to Screen Share
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4 text-gray-800 dark:text-gray-200">
            Live Screen Share
          </h1>
          <div className="inline-block p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <p className="text-sm text-gray-600 dark:text-gray-400">Session ID:</p>
            <p className="text-xl font-mono font-bold text-blue-600 dark:text-blue-400">
              {userId}
            </p>
            <div className="flex items-center justify-center gap-2 mt-2">
              <div className={`w-2 h-2 rounded-full ${isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {isLive ? 'LIVE' : 'PAUSED'}
              </span>
              {lastUpdate && (
                <span className="text-xs text-gray-500 ml-2">
                  Updated: {lastUpdate.toLocaleTimeString()}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Live Video Display */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="relative">
            <video
              ref={videoRef}
              className="w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700"
              controls
              playsInline
              autoPlay
              muted
              onError={handleImageError}
              onLoadedData={handleImageLoad}
            >
              Your browser does not support the video tag.
            </video>

            {/* Hidden canvas for stream generation */}
            <canvas
              ref={canvasRef}
              className="hidden"
            />

            {/* Live indicator overlay */}
            <div className="absolute top-4 left-4 flex items-center gap-2 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm">
              <div className={`w-2 h-2 rounded-full ${isLive && streamReady ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
              {isLive && streamReady ? 'LIVE' : streamReady ? 'PAUSED' : 'LOADING'}
            </div>
          </div>

          {/* Controls */}
          <div className="mt-6 flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={toggleLive}
              className={`px-6 py-3 rounded-lg transition-colors text-center font-medium ${isLive
                ? 'bg-red-500 text-white hover:bg-red-600'
                : 'bg-green-500 text-white hover:bg-green-600'
                }`}
            >
              {isLive ? 'Pause Live Feed' : 'Resume Live Feed'}
            </button>
            <Link
              href="/"
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-center"
            >
              Start Your Own Share
            </Link>
          </div>
        </div>

        {/* Footer Info */}
        <div className="text-center mt-8 text-sm text-gray-500 dark:text-gray-400">
          <p>Real-time video stream updates every second when new screenshots are uploaded for this session.</p>
          <p className="mt-1">Use the pause button to stop the live stream or video controls for playback.</p>
        </div>
      </div>
    </div>
  );
}
