"use client";

import Image from "next/image";
import { useState, useRef } from "react";
import { uploadScreenshot } from "./actions/upload-screenshot";

export default function Home() {
  const [screenShareActive, setScreenShareActive] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const startScreenShare = async () => {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({ video: true });
      if (!stream) {
        console.error("No stream received from getDisplayMedia.");
        return;
      }

      if (!videoRef.current) {
        console.error("Video element not available.");
        return;
      }

      videoRef.current.srcObject = stream;
      videoRef.current.onloadedmetadata = () => {
        videoRef.current?.play();
      };
      setScreenShareActive(true);
    } catch (error) {
      console.error("Error starting screen share:", error);
    }
  };

  const captureScreenshot = async () => {
    if (!videoRef.current || !canvasRef.current) {
      console.error("Video or canvas element not available.");
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.error("Canvas context not available.");
      return;
    }

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the current video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob
    return new Promise<Blob | null>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob);
      }, 'image/png');
    });
  };

  const handleScreenshotUpload = async () => {
    setIsUploading(true);
    setUploadResult(null);

    try {
      const blob = await captureScreenshot();
      if (!blob) {
        throw new Error('Failed to capture screenshot');
      }

      // Create form data
      const formData = new FormData();
      formData.append('screenshot', blob, 'screenshot.png');

      // Upload using server action
      const result = await uploadScreenshot(formData);

      if (result.success) {
        setUploadResult(`Screenshot uploaded successfully! URL: ${result.url}`);
      } else {
        setUploadResult(`Upload failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error uploading screenshot:', error);
      setUploadResult('Failed to upload screenshot');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <Image
          className="dark:invert"
          src="/next.svg"
          alt="Next.js logo"
          width={180}
          height={38}
          priority
        />
        <ol className="list-inside list-decimal text-sm/6 text-center sm:text-left font-[family-name:var(--font-geist-mono)]">
          <li className="mb-2 tracking-[-.01em]">
            Get started by editing{" "}
            <code className="bg-black/[.05] dark:bg-white/[.06] px-1 py-0.5 rounded font-[family-name:var(--font-geist-mono)] font-semibold">
              src/app/page.tsx
            </code>
            .
          </li>
          <li className="tracking-[-.01em]">
            Save and see your changes instantly.
          </li>
        </ol>

        <div className="flex gap-4 items-center flex-col sm:flex-row">
          <a
            className="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto"
            href="https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Image
              className="dark:invert"
              src="/vercel.svg"
              alt="Vercel logomark"
              width={20}
              height={20}
            />
            Deploy now
          </a>
          <a
            className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]"
            href="https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            Read our docs
          </a>
        </div>

        <div className="flex flex-col items-center justify-center mt-8">
          <h1 className="text-2xl font-bold mb-4">Screen Share</h1>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={startScreenShare}
          >
            Start Screen Share
          </button>
          <video
            ref={videoRef}
            className={`border rounded mt-4 ${screenShareActive ? "block" : "hidden"}`}
            width="640"
            height="360"
            controls
          ></video>

          {screenShareActive && (
            <div className="mt-4 flex flex-col items-center gap-2">
              <button
                className={`px-4 py-2 rounded text-white ${isUploading
                    ? "bg-gray-500 cursor-not-allowed"
                    : "bg-green-500 hover:bg-green-600"
                  }`}
                onClick={handleScreenshotUpload}
                disabled={isUploading}
              >
                {isUploading ? "Uploading..." : "Take Screenshot"}
              </button>

              {uploadResult && (
                <div className={`text-sm p-2 rounded max-w-md text-center ${uploadResult.includes('successfully')
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                  }`}>
                  {uploadResult}
                </div>
              )}
            </div>
          )}

          {/* Hidden canvas for screenshot capture */}
          <canvas
            ref={canvasRef}
            className="hidden"
          ></canvas>
        </div>
      </main>
      <footer className="row-start-3 flex gap-[24px] flex-wrap items-center justify-center">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Learn
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Go to nextjs.org →
        </a>
      </footer>
    </div>
  );
}
